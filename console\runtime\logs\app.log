2025-05-08 15:07:57 [-][-][-][error][TypeError] TypeError: in_array(): Argument #2 ($haystack) must be of type array, bool given in E:\projects\KOPHENIX_OA_PHP\console\controllers\TestController.php:18
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\console\controllers\TestController.php(18): in_array()
#1 [internal function]: console\controllers\TestController->actionIndex()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#9 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#10 {main}
2025-05-08 15:07:57 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_2588_BDWFEMMDGMOXEJZW'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\Windows\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => 'c80e830dd36e43d1b38c58d887220929'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Windows'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Windows'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '0.49.6'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1746688077.4194
    'REQUEST_TIME' => 1746688077
    'argv' => [
        0 => 'yii'
        1 => 'test/index'
    ]
    'argc' => 2
]
2025-05-08 15:13:06 [-][-][-][error][Error] Error: Cannot use a scalar value as an array in E:\projects\KOPHENIX_OA_PHP\console\controllers\TestController.php:28
Stack trace:
#0 [internal function]: console\controllers\TestController->actionIndex()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#8 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#9 {main}
2025-05-08 15:13:06 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_2588_BDWFEMMDGMOXEJZW'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\Windows\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => 'c80e830dd36e43d1b38c58d887220929'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Windows'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Windows'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '0.49.6'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1746688386.5501
    'REQUEST_TIME' => 1746688386
    'argv' => [
        0 => 'yii'
        1 => 'test/index'
    ]
    'argc' => 2
]
2025-06-05 12:42:54 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request "shell". in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php:561
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#3 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#4 {main}

Next yii\console\UnknownCommandException: Unknown command "shell". in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#2 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#3 {main}
2025-06-05 12:42:54 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_93728_RGQAEXMSNNINBZXZ'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\Windows\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => '050d8b4fd4a64152935faebff8f2d079'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\Windows\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\Windows'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Windows'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.0.0'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1749098574.0104
    'REQUEST_TIME' => 1749098574
    'argv' => [
        0 => 'yii'
        1 => 'shell'
    ]
    'argc' => 2
]
2025-06-12 10:21:46 [-][-][-][error][yii\console\Exception] yii\console\Exception: 未知的选项：--filename. Options available: --color, --interactive, --help, --silentExitOnException in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php:171
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#4 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#5 {main}
2025-06-12 10:21:46 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_16764_FBRVGHAGFYDWJAAW'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_3456' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.100.3'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1749694906.8705
    'REQUEST_TIME' => 1749694906
    'argv' => [
        0 => 'yii'
        1 => 'test/create-test-docx'
        2 => '--filename=test.docx'
    ]
    'argc' => 3
]
2025-06-12 10:22:10 [-][-][-][error][yii\console\Exception] yii\console\Exception: 未知的选项：--source. Options available: --color, --interactive, --help, --silentExitOnException in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php:171
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#4 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#5 {main}
2025-06-12 10:22:10 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_16764_FBRVGHAGFYDWJAAW'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_3456' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.100.3'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1749694930.1535
    'REQUEST_TIME' => 1749694930
    'argv' => [
        0 => 'yii'
        1 => 'test/convert-docx-to-pdf'
        2 => '--source=test.docx'
        3 => '--target=test.pdf'
    ]
    'argc' => 4
]
2025-06-12 10:45:06 [-][-][-][error][ParseError] ParseError: syntax error, unexpected single-quoted string "（）【】《》", expecting ")" in E:\projects\KOPHENIX_OA_PHP\console\controllers\TestController.php:933
Stack trace:
#0 [internal function]: yii\BaseYii::autoload()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(661): class_exists()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(620): yii\base\Module->createControllerByID()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(546): yii\base\Module->createController()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#7 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#8 {main}
2025-06-12 10:45:06 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_40648_UKNMIXVVWQBXTYCD'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_17400' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.100.3'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1749696306.7754
    'REQUEST_TIME' => 1749696306
    'argv' => [
        0 => 'yii'
        1 => 'test/create-chinese-font-test'
    ]
    'argc' => 2
]
2025-06-12 10:45:22 [-][-][-][error][ParseError] ParseError: syntax error, unexpected single-quoted string "（）【】《》", expecting ")" in E:\projects\KOPHENIX_OA_PHP\console\controllers\TestController.php:933
Stack trace:
#0 [internal function]: yii\BaseYii::autoload()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(661): class_exists()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(620): yii\base\Module->createControllerByID()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(546): yii\base\Module->createController()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#7 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#8 {main}
2025-06-12 10:45:22 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_40648_UKNMIXVVWQBXTYCD'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_17400' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.100.3'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1749696322.0217
    'REQUEST_TIME' => 1749696322
    'argv' => [
        0 => 'yii'
        1 => 'test/create-chinese-font-test'
    ]
    'argc' => 2
]
2025-06-12 10:45:56 [-][-][-][error][ParseError] ParseError: syntax error, unexpected single-quoted string "（）【】《》", expecting ")" in E:\projects\KOPHENIX_OA_PHP\console\controllers\TestController.php:933
Stack trace:
#0 [internal function]: yii\BaseYii::autoload()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(661): class_exists()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(620): yii\base\Module->createControllerByID()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(546): yii\base\Module->createController()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#7 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#8 {main}
2025-06-12 10:45:56 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_40648_UKNMIXVVWQBXTYCD'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_17400' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.100.3'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1749696356.6055
    'REQUEST_TIME' => 1749696356
    'argv' => [
        0 => 'yii'
        1 => 'test/create-chinese-font-test'
    ]
    'argc' => 2
]
2025-06-16 16:20:56 [-][-][-][error][yii\console\Exception] yii\console\Exception: 未知的选项：--source-user-id. Options available: --color, --interactive, --help, --silentExitOnException in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php:171
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#4 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#5 {main}
2025-06-16 16:20:56 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_51652_MLULYFXWQAFVLNDD'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_19452_1592913036' => '1'
    'EFC_19452_2283032206' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.0'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1750062056.324
    'REQUEST_TIME' => 1750062056
    'argv' => [
        0 => 'yii'
        1 => 'test/copy-user-permissions'
        2 => '--source-user-id=265'
        3 => '--target-org-id=201'
    ]
    'argc' => 4
]
2025-06-24 11:44:47 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request: test/create-sales-contract-template in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php:149
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#5 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#6 {main}

Next yii\console\UnknownCommandException: Unknown command "test/create-sales-contract-template". in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#2 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#3 {main}
2025-06-24 11:44:46 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_49552_GRBZUSJAIHNGUQEG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_18916_1592913036' => '1'
    'EFC_18916_2283032206' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1750736686.6594
    'REQUEST_TIME' => 1750736686
    'argv' => [
        0 => 'yii'
        1 => 'test/create-sales-contract-template'
    ]
    'argc' => 2
]
2025-07-09 09:27:20 [-][-][-][error][yii\console\UnknownCommandException] yii\base\InvalidRouteException: Unable to resolve the request: test/test-organize-copy in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php:149
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#5 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#6 {main}

Next yii\console\UnknownCommandException: Unknown command "test/test-organize-copy". in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php:183
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#2 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#3 {main}
2025-07-09 09:27:20 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_102740_MTNZJPAHMMRKOXTC'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_18916_1592913036' => '1'
    'EFC_18916_2283032206' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.101.2'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1752024439.9115
    'REQUEST_TIME' => 1752024439
    'argv' => [
        0 => 'yii'
        1 => 'test/test-organize-copy'
    ]
    'argc' => 2
]
2025-07-10 14:36:44 [-][-][-][error][Exception] Exception: Cannot find archive file. in E:\projects\KOPHENIX_OA_PHP\vendor\phpoffice\phpword\src\PhpWord\Shared\XMLReader.php:61
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\phpoffice\phpword\src\PhpWord\Reader\Word2007.php(169): PhpOffice\PhpWord\Shared\XMLReader->getDomFromZip()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\phpoffice\phpword\src\PhpWord\Reader\Word2007.php(133): PhpOffice\PhpWord\Reader\Word2007->getRels()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\phpoffice\phpword\src\PhpWord\Reader\Word2007.php(48): PhpOffice\PhpWord\Reader\Word2007->readRelationships()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\phpoffice\phpword\src\PhpWord\IOFactory.php(92): PhpOffice\PhpWord\Reader\Word2007->load()
#4 E:\projects\KOPHENIX_OA_PHP\console\controllers\TestController.php(1906): PhpOffice\PhpWord\IOFactory::load()
#5 [internal function]: console\controllers\TestController->actionTestWord()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#13 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#14 {main}
2025-07-10 14:36:43 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_90052_FUKQFDJLUHVQGEMS'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => 'dbc9074e899e4011abf89f9816fec967'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_18916_1592913036' => '1'
    'EFC_18916_2283032206' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.2.2'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1752129403.6641
    'REQUEST_TIME' => 1752129403
    'argv' => [
        0 => 'yii'
        1 => 'test/test-word'
    ]
    'argc' => 2
]
2025-07-23 11:53:01 [-][-][-][error][TypeError] TypeError: iterator_to_array(): Argument #1 ($iterator) must be of type Traversable, array given in E:\projects\KOPHENIX_OA_PHP\test_mongodb_sku.php:41
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\test_mongodb_sku.php(41): iterator_to_array()
#1 {main}
2025-07-23 11:53:01 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_72716_KUVRZFFBKFWUINVH'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => '87bee6a4606d4cad86bcda2c26218cd7'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin;c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.2.4'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'test_mongodb_sku.php'
    'SCRIPT_NAME' => 'test_mongodb_sku.php'
    'SCRIPT_FILENAME' => 'test_mongodb_sku.php'
    'PATH_TRANSLATED' => 'test_mongodb_sku.php'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753242780.8049
    'REQUEST_TIME' => 1753242780
    'argv' => [
        0 => 'test_mongodb_sku.php'
    ]
    'argc' => 1
]
2025-07-23 11:55:35 [-][-][-][error][TypeError] TypeError: Cannot access offset of type string on string in E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php:1173
Stack trace:
#0 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#8 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#9 {main}
2025-07-23 11:53:42 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_72716_KUVRZFFBKFWUINVH'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => '87bee6a4606d4cad86bcda2c26218cd7'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin;c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.2.4'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753242822.0482
    'REQUEST_TIME' => 1753242822
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-01-20'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 11:56:57 [-][-][-][error][TypeError] TypeError: Cannot access offset of type string on string in E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php:1173
Stack trace:
#0 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#8 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#9 {main}
2025-07-23 11:56:03 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_72716_KUVRZFFBKFWUINVH'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => '87bee6a4606d4cad86bcda2c26218cd7'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin;c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.2.4'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753242963.6094
    'REQUEST_TIME' => 1753242963
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-01-20'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 11:58:16 [-][-][-][error][TypeError] TypeError: Cannot access offset of type string on string in E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php:1173
Stack trace:
#0 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#8 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#9 {main}
2025-07-23 11:58:14 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_72716_KUVRZFFBKFWUINVH'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => '87bee6a4606d4cad86bcda2c26218cd7'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin;c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.2.4'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753243094.0618
    'REQUEST_TIME' => 1753243094
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-01-20'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 11:59:36 [-][-][-][error][TypeError] TypeError: Cannot access offset of type string on string in E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php:1176
Stack trace:
#0 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#8 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#9 {main}
2025-07-23 11:59:34 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_72716_KUVRZFFBKFWUINVH'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CURSOR_TRACE_ID' => '87bee6a4606d4cad86bcda2c26218cd7'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\bin;c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.2.4'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7928230c04-sock'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.cursor\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753243173.9823
    'REQUEST_TIME' => 1753243173
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-01-20'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:26:02 [-][-][-][error][yii\base\ErrorException:64] yii\base\ErrorException: Cannot redeclare common\models\mongo\TradeDelivery::attributes() in E:\projects\KOPHENIX_OA_PHP\common\models\mongo\TradeDelivery.php:27
Stack trace:
#0 [internal function]: yii\base\ErrorHandler->handleFatalError()
#1 {main}
2025-07-23 12:26:00 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_74288_XMMFNDLFHLKSZORV'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'kiro'
    'TERM_PROGRAM_VERSION' => '0.1.15'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\Kiro.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7da1e00d7c-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753244760.6212
    'REQUEST_TIME' => 1753244760
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:33:54 [-][-][-][error][ParseError] ParseError: syntax error, unexpected token "public" in E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php:1129
Stack trace:
#0 [internal function]: yii\BaseYii::autoload()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(661): class_exists()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(620): yii\base\Module->createControllerByID()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(546): yii\base\Module->createController()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#7 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#8 {main}
2025-07-23 12:33:54 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_74288_XMMFNDLFHLKSZORV'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'kiro'
    'TERM_PROGRAM_VERSION' => '0.1.15'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\Kiro.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7da1e00d7c-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753245233.9788
    'REQUEST_TIME' => 1753245233
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:34:55 [-][-][-][error][ParseError] ParseError: syntax error, unexpected token "public" in E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php:1129
Stack trace:
#0 [internal function]: yii\BaseYii::autoload()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(661): class_exists()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(620): yii\base\Module->createControllerByID()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(546): yii\base\Module->createController()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#7 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#8 {main}
2025-07-23 12:34:55 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_74288_XMMFNDLFHLKSZORV'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'kiro'
    'TERM_PROGRAM_VERSION' => '0.1.15'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\Kiro.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Kiro\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-7da1e00d7c-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753245294.769
    'REQUEST_TIME' => 1753245294
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:41:46 [-][-][-][error][Error] Error: Class "console\controllers\VarDumper" not found in E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php:1001
Stack trace:
#0 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#8 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#9 {main}
2025-07-23 12:41:43 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753245703.6007
    'REQUEST_TIME' => 1753245703
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:55:28 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 12:55:28 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1003): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') AND (`td`.`pay_time` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split`, LEFT(td.pay_time, `10)` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1003): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 12:55:27 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246526.779
    'REQUEST_TIME' => 1753246526
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:56:11 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 12:56:11 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1003): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') AND (`td`.`pay_time` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split`, LEFT(td.pay_time, `10)` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1003): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 12:56:10 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246569.9238
    'REQUEST_TIME' => 1753246569
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:58:26 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 12:58:26 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1012): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1012): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 12:58:24 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246704.6813
    'REQUEST_TIME' => 1753246704
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:59:00 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 12:59:00 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1012): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1012): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 12:58:58 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246737.7772
    'REQUEST_TIME' => 1753246737
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:59:24 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 12:59:24 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 12:59:22 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246762.5457
    'REQUEST_TIME' => 1753246762
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:59:35 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 12:59:35 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 12:59:33 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246773.4006
    'REQUEST_TIME' => 1753246773
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 12:59:59 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 12:59:59 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 12:59:57 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246796.7848
    'REQUEST_TIME' => 1753246796
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 13:00:22 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 13:00:22 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 13:00:20 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246820.4908
    'REQUEST_TIME' => 1753246820
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 13:00:48 [-][-][-][error][yii\db\Exception] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1010): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty`, LEFT(td.pay_time, `10)` AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1010): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_d' at line 1
)

2025-07-23 13:00:45 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246845.6815
    'REQUEST_TIME' => 1753246845
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 13:02:07 [-][-][-][error][yii\db\Exception] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'ORDER BY `td`.`mallId`' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeReturnQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1079): common\models\GuanyierpTradeReturnQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'ORDER BY `td`.`mallId`' at line 1
The SQL being executed was: SELECT `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, COUNT(DISTINCT td.id) AS `refund_num`, sum(d.amount_after) AS `refund_amount`, sum(d.qty) AS `refund_qty`, LEFT(td.pay_time,10) AS `pay_date` FROM `guanyierp_trade_return` `r` INNER JOIN `guanyierp_trade_deliverys` `td` ON r.platform_code=td.platform_code LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`r`.`create_time` >= 1752681600) AND (`r`.`create_time` < 1752768000) AND (`d`.`amount_after` > 0) AND (`d`.`combine_item_code_split` != '') AND (`td`.`pay_time` != '') GROUP BY `d`.`platform_goods_id`, `d`.`combine_item_code_split`, LEFT(td.pay_time, `10)` ORDER BY `td`.`mallId` in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeReturnQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1079): common\models\GuanyierpTradeReturnQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'ORDER BY `td`.`mallId`' at line 1
)

2025-07-23 13:02:04 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753246924.6203
    'REQUEST_TIME' => 1753246924
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 13:06:34 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 13:06:34 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1
The SQL being executed was: SELECT LEFT(td.pay_time, `10)` AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY LEFT(td.pay_time, `10)`, `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1
)

2025-07-23 13:06:32 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753247192.1586
    'REQUEST_TIME' => 1753247192
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 13:07:38 [-][-][-][error][application] sku的增量数据：2025-07-17
2025-07-23 13:07:38 [-][-][-][error][application] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1
The SQL being executed was: SELECT LEFT(td.pay_time, `10)` AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY LEFT(td.pay_time, `10)`, `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1011): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1
)

2025-07-23 13:07:36 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753247255.9028
    'REQUEST_TIME' => 1753247255
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 13:07:54 [-][-][-][error][yii\db\Exception] PDOException: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1 in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php:1320
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1320): PDOStatement->execute()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1010): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}

Next yii\db\Exception: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1
The SQL being executed was: SELECT LEFT(td.pay_time, `10)` AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, COUNT(DISTINCT td.id) AS `order_num`, `platform_item_name`, `d`.`platform_goods_id` AS `goods_id`, `d`.`combine_item_code_split` AS `sku_code`, `d`.`combine_item_name_split` AS `sku_name`, sum(d.amount_after) AS `amount_after`, sum(d.post_cost) AS `post_cost`, sum(d.total_cost_price) AS `product_cost`, sum(d.qty) AS `qty` FROM `guanyierp_trade_deliverys` `td` LEFT JOIN `guanyierp_trade_deliverys_details` `d` ON d.pid=td.id WHERE (`td`.`delivery_date` >= '2025-07-17') AND (`td`.`delivery_date` < '2025-07-18') AND (`d`.`combine_item_code_split` != '') GROUP BY LEFT(td.pay_time, `10)`, `d`.`platform_goods_id`, `d`.`combine_item_code_split` ORDER BY `d`.`combine_item_code_split` DESC in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Schema.php:676
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1325): yii\db\Schema->convertException()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(1186): yii\db\Command->internalExecute()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Command.php(417): yii\db\Command->queryInternal()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\Command->queryAll()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#5 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#6 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1010): common\models\GuanyierpTradeDeliverysQuery->all()
#7 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#11 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#12 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#13 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#15 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#16 {main}
Additional Information:
Array
(
    [0] => 42000
    [1] => 1064
    [2] => You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'AS `pay_date`, `td`.`mallId`, `td`.`shop_code`, `mallPlatform` AS `platform`, CO' at line 1
)

2025-07-23 13:07:52 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753247272.1635
    'REQUEST_TIME' => 1753247272
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 13:19:11 [-][-][-][error][TypeError] TypeError: date(): Argument #2 ($timestamp) must be of type ?int, string given in E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php:1005
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(1005): date()
#1 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#9 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#10 {main}
2025-07-23 13:19:08 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753247948.5974
    'REQUEST_TIME' => 1753247948
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
2025-07-23 13:46:58 [-][-][-][error][yii\base\InvalidArgumentException] yii\base\InvalidArgumentException: Operator 'DISTINCT LEFT(TD.PAY_TIME,10) = ''' requires two operands. in E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\conditions\SimpleCondition.php:80
Stack trace:
#0 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\QueryBuilder.php(1606): yii\db\conditions\SimpleCondition::fromArrayDefinition()
#1 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\QueryBuilder.php(1576): yii\db\QueryBuilder->createConditionFromArray()
#2 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\conditions\ConjunctionConditionBuilder.php(60): yii\db\QueryBuilder->buildCondition()
#3 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\conditions\ConjunctionConditionBuilder.php(35): yii\db\conditions\ConjunctionConditionBuilder->buildExpressionsFrom()
#4 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\QueryBuilder.php(288): yii\db\conditions\ConjunctionConditionBuilder->build()
#5 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\QueryBuilder.php(1580): yii\db\QueryBuilder->buildExpression()
#6 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\QueryBuilder.php(1357): yii\db\QueryBuilder->buildCondition()
#7 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\QueryBuilder.php(235): yii\db\QueryBuilder->buildWhere()
#8 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(328): yii\db\QueryBuilder->build()
#9 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\Query.php(249): yii\db\ActiveQuery->createCommand()
#10 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\db\ActiveQuery.php(133): yii\db\Query->all()
#11 E:\projects\KOPHENIX_OA_PHP\common\models\GuanyierpTradeDeliverysQuery.php(23): yii\db\ActiveQuery->all()
#12 E:\projects\KOPHENIX_OA_PHP\console\controllers\Guanyierp2Controller.php(995): common\models\GuanyierpTradeDeliverysQuery->all()
#13 [internal function]: console\controllers\Guanyierp2Controller->actionSku()
#14 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\InlineAction.php(57): call_user_func_array()
#15 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Controller.php(178): yii\base\InlineAction->runWithParams()
#16 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Controller.php(180): yii\base\Controller->runAction()
#17 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction()
#18 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction()
#19 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction()
#20 E:\projects\KOPHENIX_OA_PHP\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest()
#21 E:\projects\KOPHENIX_OA_PHP\yii(27): yii\base\Application->run()
#22 {main}
2025-07-23 13:46:56 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_27068_ITWMHCWNRTHRHKCG'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'DESKTOP-4BCF709'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CUDA_PATH' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'CUDA_PATH_V12_9' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_20256_1262719628' => '1'
    'EFC_20256_1592913036' => '1'
    'EFC_20256_2283032206' => '1'
    'EFC_20256_2775293581' => '1'
    'EFC_20256_3789132940' => '1'
    'GOPATH' => 'C:\\Users\\<USER>\\go'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\FAITH'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\DESKTOP-4BCF709'
    'NUMBER_OF_PROCESSORS' => '96'
    'NVM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\nvm'
    'NVM_SYMLINK' => 'C:\\nvm4w\\nodejs'
    'OLLAMA_MODELS' => 'E:\\ollama'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\bin;C:\\Program Files\\NVIDIA GPU Computing Toolkit\\CUDA\\v12.9\\libnvvp;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Program Files\\Bandizip\\;C:\\Program Files\\Microsoft SQL Server\\150\\Tools\\Binn\\;C:\\Program Files\\Microsoft SQL Server\\Client SDK\\ODBC\\170\\Tools\\Binn\\;C:\\Program Files\\NVIDIA Corporation\\NVIDIA app\\NvDLISR;C:\\Program Files (x86)\\NVIDIA Corporation\\PhysX\\Common;C:\\Program Files\\Git\\cmd;C:\\Program Files\\NVIDIA Corporation\\Nsight Compute 2025.2.0\\;C:\\Program Files (x86)\\Windows Kits\\10\\Windows Performance Toolkit\\;C:\\Program Files\\Go\\bin;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\Trae CN\\bin;C:\\Users\\<USER>\\AppData\\Local\\pnpm;C:\\Users\\<USER>\\.local\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\nvm;C:\\nvm4w\\nodejs;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Ollama;E:\\phpstudy_pro\\Extensions\\php\\php8.0.2nts;C:\\Users\\<USER>\\.bun\\bin;C:\\Users\\<USER>\\.dotnet\\tools;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\go\\bin;E:\\bin;;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\scripts\\noConfigScripts'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.CPL'
    'PNPM_HOME' => 'C:\\Users\\<USER>\\AppData\\Local\\pnpm'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'AMD64 Family 25 Model 17 Stepping 2, AuthenticAMD'
    'PROCESSOR_LEVEL' => '25'
    'PROCESSOR_REVISION' => '1102'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'DESKTOP-4BCF709'
    'USERDOMAIN_ROAMINGPROFILE' => 'DESKTOP-4BCF709'
    'USERNAME' => 'FAITH'
    'USERPROFILE' => 'C:\\Users\\<USER>\\WINDOWS'
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '1.102.1'
    'LANG' => 'zh_CN.UTF-8'
    'COLORTERM' => 'truecolor'
    'PYDEVD_DISABLE_FILE_VALIDATION' => '1'
    'VSCODE_DEBUGPY_ADAPTER_ENDPOINTS' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\.noConfigDebugAdapterEndpoints\\endpoint-937dc95a92ffd80c.txt'
    'BUNDLED_DEBUGPY_PATH' => 'c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.8.0-win32-x64\\bundled\\libs\\debugpy'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-f2cd5f7838-sock'
    'VSCODE_INJECTION' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1753249615.9287
    'REQUEST_TIME' => 1753249615
    'argv' => [
        0 => 'yii'
        1 => 'guanyierp2/sku'
        2 => '2025-07-17'
        3 => '1'
    ]
    'argc' => 4
]
